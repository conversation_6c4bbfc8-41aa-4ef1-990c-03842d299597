<?php
/**
 * Standalone elFinder File Manager
 * Access this directly without going through the main application
 */

// Load composer autoload
require_once __DIR__ . '/app/vendor/composer/autoload.php';

// Debug paths if requested
if (isset($_GET['debug_paths'])) {
    $filesPath = __DIR__ . '/files/';
    $filesRealPath = realpath($filesPath);
    $trashPath = __DIR__ . '/files/.trash/';
    $trashRealPath = realpath($trashPath);

    echo "Files path: $filesPath\n";
    echo "Files real path: $filesRealPath\n";
    echo "Trash path: $trashPath\n";
    echo "Trash real path: $trashRealPath\n";
    echo "Files exists: " . (is_dir($filesRealPath) ? 'YES' : 'NO') . "\n";
    echo "Files readable: " . (is_readable($filesRealPath) ? 'YES' : 'NO') . "\n";
    echo "Files writable: " . (is_writable($filesRealPath) ? 'YES' : 'NO') . "\n";
    exit;
}

// Check if this is a connector request
if (isset($_GET['connector']) || (isset($_SERVER['REQUEST_URI']) && strpos($_SERVER['REQUEST_URI'], 'connector') !== false)) {
    // Handle elFinder connector requests
    error_reporting(0); // Disable error reporting for production

    // Enable FTP connector netmount
    elFinder::$netDrivers['ftp'] = 'FTP';

    // Access control function
    function access($attr, $path, $data, $volume, $isDir, $relpath) {
        $basename = basename($path);
        return $basename[0] === '.'                  // if file/folder begins with '.' (dot)
                 && strlen($relpath) !== 1           // but without volume root
            ? !($attr == 'read' || $attr == 'write') // set read+write to false, other (locked+hidden) set to true
            :  null;                                 // else elFinder decide it itself
    }

    // Debug paths
    $filesPath = __DIR__ . '/files/';
    $filesRealPath = realpath($filesPath);
    $trashPath = __DIR__ . '/files/.trash/';
    $trashRealPath = realpath($trashPath);


    // Configuration options
    $opts = array(
        // 'debug' => true,
        'roots' => array(
            // Items volume
            array(
                'driver'        => 'LocalFileSystem',           // driver for accessing file system (REQUIRED)
                'path'          => $filesRealPath,              // path to files (REQUIRED)
                'URL'           => '/files/',                   // URL to files (REQUIRED)
                'trashHash'     => 't1_Lw',                     // elFinder's hash of trash folder
                'winHashFix'    => DIRECTORY_SEPARATOR !== '/', // to make hash same to Linux one on windows too
                'uploadDeny'    => array('all'),                // All Mimetypes not allowed to upload
                'uploadAllow'   => array(
                    'image/x-ms-bmp', 'image/gif', 'image/jpeg', 'image/png', 'image/x-icon', 
                    'text/plain', 'text/html', 'text/css', 'text/javascript', 'application/javascript',
                    'application/pdf', 'application/zip', 'application/x-zip-compressed',
                    'video/mp4', 'video/avi', 'video/quicktime', 'audio/mpeg', 'audio/wav'
                ), // Extended allowed file types
                'uploadOrder'   => array('deny', 'allow'),      // allowed Mimetype processing order
                'accessControl' => 'access'                     // disable and hide dot starting files (OPTIONAL)
            ),
            // Trash volume
            array(
                'id'            => '1',
                'driver'        => 'Trash',
                'path'          => $trashRealPath,
                'tmbURL'        => '/files/.trash/.tmb/',
                'winHashFix'    => DIRECTORY_SEPARATOR !== '/', // to make hash same to Linux one on windows too
                'uploadDeny'    => array('all'),                // Recommend the same settings as the original volume that uses the trash
                'uploadAllow'   => array(
                    'image/x-ms-bmp', 'image/gif', 'image/jpeg', 'image/png', 'image/x-icon', 
                    'text/plain', 'text/html', 'text/css', 'text/javascript', 'application/javascript',
                    'application/pdf', 'application/zip', 'application/x-zip-compressed',
                    'video/mp4', 'video/avi', 'video/quicktime', 'audio/mpeg', 'audio/wav'
                ), // Same as above
                'uploadOrder'   => array('deny', 'allow'),      // Same as above
                'accessControl' => 'access',                    // Same as above
            ),
        )
    );

    // Run elFinder
    $connector = new elFinderConnector(new elFinder($opts));
    $connector->run();
    exit;
}

// Render the file manager interface
$elfinderPath = '/app/vendor/composer/studio-42/elfinder';
?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>File Manager - VisualBook</title>
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=2" />
    
    <!-- jQuery (REQUIRED) -->
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
    
    <!-- elFinder CSS (REQUIRED) -->
    <link rel="stylesheet" type="text/css" href="<?= $elfinderPath ?>/css/elfinder.min.css">
    <link rel="stylesheet" type="text/css" href="<?= $elfinderPath ?>/css/theme.css">

    <!-- elFinder JS (REQUIRED) -->
    <script src="<?= $elfinderPath ?>/js/elfinder.min.js"></script>

    <!-- elFinder translation (OPTIONAL) -->
    <script src="<?= $elfinderPath ?>/js/i18n/elfinder.en.js"></script>
    
    <style>
        body { margin: 0; padding: 20px; font-family: Arial, sans-serif; background: #f5f5f5; }
        .header { background: #fff; padding: 15px; margin-bottom: 20px; border-radius: 5px; box-shadow: 0 2px 5px rgba(0,0,0,0.1); }
        h1 { color: #333; margin: 0; }
        .nav { margin-top: 10px; }
        .nav a { color: #007cba; text-decoration: none; margin-right: 15px; }
        .nav a:hover { text-decoration: underline; }
        #elfinder { border: 1px solid #ccc; border-radius: 5px; background: #fff; }
    </style>
</head>
<body>
    <div class="header">
        <h1>📁 File Manager</h1>
        <div class="nav">
            <a href="/">← Back to Home</a>
            <a href="/admin">Admin Panel</a>
        </div>
    </div>
    
    <!-- Element where elFinder will be created (REQUIRED) -->
    <div id="elfinder"></div>

    <script type="text/javascript">
        $(document).ready(function() {
            $('#elfinder').elfinder({
                url : '/filemanager.php?connector=1',  // connector URL (REQUIRED)
                lang: 'en',                            // language (OPTIONAL)
                width: '100%',                         // manager width
                height: 600,                           // manager height
                resizable: true                        // enable resizing
            });
        });
    </script>

</body>
</html>

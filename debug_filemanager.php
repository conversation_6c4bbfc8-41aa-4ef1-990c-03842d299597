<?php

// Simple debug script to test elFinder setup
require_once __DIR__ . '/app/vendor/composer/autoload.php';

echo "Testing elFinder setup...\n";

// Check if elFinder classes exist
if (class_exists('elFinder')) {
    echo "✓ elFinder class found\n";
} else {
    echo "✗ elFinder class NOT found\n";
}

if (class_exists('elFinderConnector')) {
    echo "✓ elFinderConnector class found\n";
} else {
    echo "✗ elFinderConnector class NOT found\n";
}

// Check if files directory exists
$filesPath = __DIR__ . '/files/';
if (is_dir($filesPath)) {
    echo "✓ Files directory exists: $filesPath\n";
    echo "✓ Files directory is " . (is_writable($filesPath) ? 'writable' : 'NOT writable') . "\n";
} else {
    echo "✗ Files directory NOT found: $filesPath\n";
}

// Check trash directory
$trashPath = __DIR__ . '/files/.trash/';
if (is_dir($trashPath)) {
    echo "✓ Trash directory exists: $trashPath\n";
    echo "✓ Trash directory is " . (is_writable($trashPath) ? 'writable' : 'NOT writable') . "\n";
} else {
    echo "✗ Trash directory NOT found: $trashPath\n";
}

echo "\nDone.\n";

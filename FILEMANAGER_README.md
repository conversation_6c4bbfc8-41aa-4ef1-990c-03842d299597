# elFinder File Manager Setup

## Overview
The elFinder file manager has been successfully integrated into the VisualBook project. It provides a web-based file management interface for uploading, organizing, and managing files.

## Access
- **Direct Access**: http://visualbook.localhost/filemanager.php
- **From Admin Panel**: Login as admin and click the "📁 File Manager" link

## Files and Directories

### Main Files
- `filemanager.php` - Standalone file manager interface and connector
- `files/` - Main directory for file storage (writable by web server)
- `files/.trash/` - Trash directory for deleted files
- `files/.tmb/` - Thumbnail cache directory

### Integration Files
- `app/front/filemanager/FileManagerController.php` - Controller for framework integration (currently not used due to database dependency)
- `app/front/admin/AdminPage.latte` - Updated to include file manager link

## Configuration

### File Types Allowed
The file manager is configured to allow these file types:
- Images: BMP, GIF, JPEG, PNG, ICO
- Text: Plain text, HTML, CSS, JavaScript
- Documents: PDF
- Archives: ZIP
- Media: MP4, AVI, QuickTime, MP3, WAV

### Security Features
- Hidden files (starting with .) are protected
- Upload restrictions by MIME type
- Trash functionality for safe file deletion
- Access control for file operations

## Permissions
The `files/` directory and its subdirectories must be writable by the web server:
```bash
chmod 777 files/ files/.trash/
```

## Troubleshooting

### Common Issues
1. **Permission Denied**: Ensure files directory is writable by web server
2. **Assets Not Loading**: Check .htaccess configuration for static file serving
3. **Database Errors**: Use standalone filemanager.php instead of framework integration

### Debug Mode
To enable debug mode, edit `filemanager.php` and change:
```php
'debug' => true,
```

## Technical Details
- Uses elFinder 2.1+ (installed via Composer)
- jQuery-based frontend interface
- PHP backend connector
- LocalFileSystem driver for file operations
- Trash volume for deleted files recovery

## Future Improvements
- Integration with user authentication system
- Per-user file directories
- File sharing capabilities
- Advanced permission management

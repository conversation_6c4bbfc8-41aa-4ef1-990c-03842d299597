/** @type {import('tailwindcss').Config} */
module.exports = {
    content: [
      './index.html', // Root HTML file
      './public/index.php',
      './*.html',     // Any other HTML files in the root directory
      './*.css',      // Any CSS files in the root directory
      './src/**/*.{html,js,jsx,ts,tsx}', // All files in src directory with specified extensions
      './articles/**/*.html',
      './legal/**/*.html',
      './public/*.php',
      './src/**/*.php',
      './public/**/*.php',  // <-- include all your PHP files
      './**/*.php',          // <-- catch any other random php
      './**/*.latte',
      './app/**/*.latte'
    ],
    safelist: [
      'text-7xl',
      'font-dm',
      'font-chivo',
    ],
    theme: {
      extend: {
        fontFamily: {
          chivo: ['"Chivo Mono"', 'monospace'],
          dm: ['"DM Sans"', 'sans-serif'],
          in: ['"Inter"', 'sans-serif'],

        },
        colors: {
          background: '#121214',
          primary: '#CFCFED',
          myred: '#FF6B79',
          myblue: '#4F9AFF',
        },
      },
    },
    plugins: [],
  }
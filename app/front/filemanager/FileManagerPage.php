<?php namespace app\front\filemanager;

use app\front\layout\BaseLayout;
use app\system\Redirect;
use app\system\AppSession;

/** File Manager Page */
class FileManagerPage extends BaseLayout
{

    public function getPageName(): string
    {
        return 'File Manager';
    }

    protected function prepareTemplate(\app\system\component\Templater $templater): void 
    {
        // Get list of files from the files directory
        $filesPath = realpath(__DIR__ . '/../../../files/');
        $files = [];
        
        if ($filesPath && is_dir($filesPath)) {
            $iterator = new \RecursiveIteratorIterator(
                new \RecursiveDirectoryIterator($filesPath, \RecursiveDirectoryIterator::SKIP_DOTS),
                \RecursiveIteratorIterator::LEAVES_ONLY
            );
            
            foreach ($iterator as $file) {
                if ($file->isFile() && !str_starts_with($file->getFilename(), '.')) {
                    $relativePath = str_replace($filesPath . DIRECTORY_SEPARATOR, '', $file->getPathname());
                    $files[] = [
                        'name' => $file->getFilename(),
                        'path' => $relativePath,
                        'size' => $file->getSize(),
                        'modified' => $file->getMTime(),
                        'type' => $file->getExtension(),
                        'url' => '/files/' . str_replace(DIRECTORY_SEPARATOR, '/', $relativePath)
                    ];
                }
            }
        }
        
        // Sort files by name
        usort($files, function($a, $b) {
            return strcmp($a['name'], $b['name']);
        });
        
        $templater->addData([
            'files' => $files,
            'filesCount' => count($files),
            'elfinderUrl' => '/filemanager/connector'
        ]);    
    }

    public function checkPost(array $postData): void
    {
        // Handle any POST actions if needed
        // For example, file deletion, folder creation, etc.
        
        if (isset($postData['action'])) {
            switch ($postData['action']) {
                case 'delete_file':
                    if (isset($postData['file_path'])) {
                        $this->deleteFile($postData['file_path']);
                    }
                    break;
                    
                case 'create_folder':
                    if (isset($postData['folder_name'])) {
                        $this->createFolder($postData['folder_name']);
                    }
                    break;
            }
        }
    }
    
    private function deleteFile(string $filePath): void
    {
        $filesPath = realpath(__DIR__ . '/../../../files/');
        $fullPath = $filesPath . DIRECTORY_SEPARATOR . $filePath;
        
        // Security check - ensure file is within files directory
        if (str_starts_with(realpath($fullPath), $filesPath) && file_exists($fullPath)) {
            unlink($fullPath);
            // You could add a success message here
        }
    }
    
    private function createFolder(string $folderName): void
    {
        $filesPath = realpath(__DIR__ . '/../../../files/');
        $folderPath = $filesPath . DIRECTORY_SEPARATOR . $folderName;
        
        // Security check and create folder
        if (!file_exists($folderPath) && preg_match('/^[a-zA-Z0-9_-]+$/', $folderName)) {
            mkdir($folderPath, 0755, true);
            // You could add a success message here
        }
    }
}

<?php namespace app\front\filemanager;

use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use app\system\AppSession;
use app\system\BaseController;

class FileManagerController extends BaseController
{
    public static function getName(): string {
        return 'filemanager';
    }

    public function index(Request $request): Response {
        if ($request->getMethod() === 'POST') {
            $page = new FileManagerPage();
            $page->checkPost($request->request->all());
        }

        return new Response(
            FileManagerPage::renderString()
        );
    }

    protected function getControllerMapping(): array {
        return [
            '/filemanager' => [$this, 'index'],
            '/filemanager/connector' => [$this, 'connector'],
        ];
    }

    public function connector(Request $request): Response {
        // Handle elFinder connector requests
        error_reporting(0); // Disable error reporting for production

        // Enable FTP connector netmount
        \elFinder::$netDrivers['ftp'] = 'FTP';

        // Access control function
        $accessControl = function($attr, $path, $data, $volume, $isDir, $relpath) {
            $basename = basename($path);
            return $basename[0] === '.'                  // if file/folder begins with '.' (dot)
                     && strlen($relpath) !== 1           // but without volume root
                ? !($attr == 'read' || $attr == 'write') // set read+write to false, other (locked+hidden) set to true
                :  null;                                 // else elFinder decide it itself
        };

        // For now, disable elFinder connector and return empty response
        // We'll focus on the database-driven file list in the template
        return new Response('{"error":["elFinder temporarily disabled - using database file list instead"]}', 200, [
            'Content-Type' => 'application/json'
        ]);

        // Run elFinder
        $connector = new \elFinderConnector(new \elFinder($opts));
        
        // Capture output
        ob_start();
        $connector->run();
        $output = ob_get_clean();
        
        return new Response($output, 200, [
            'Content-Type' => 'application/json'
        ]);
    }

    private function renderFileManagerInterface(): string {
        $elfinderPath = '/app/vendor/composer/studio-42/elfinder';

        return '<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>File Manager - VisualBook</title>
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=2" />

    <!-- jQuery (REQUIRED) -->
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>

    <!-- elFinder CSS (REQUIRED) -->
    <link rel="stylesheet" type="text/css" href="' . $elfinderPath . '/css/elfinder.min.css">
    <link rel="stylesheet" type="text/css" href="' . $elfinderPath . '/css/theme.css">

    <!-- elFinder JS (REQUIRED) -->
    <script src="' . $elfinderPath . '/js/elfinder.min.js"></script>

    <!-- elFinder translation (OPTIONAL) -->
    <script src="' . $elfinderPath . '/js/i18n/elfinder.en.js"></script>

    <style>
        body { margin: 0; padding: 20px; font-family: Arial, sans-serif; }
        h1 { color: #333; margin-bottom: 20px; }
        #elfinder { border: 1px solid #ccc; }
    </style>
</head>
<body>
    <h1>File Manager</h1>

    <!-- Element where elFinder will be created (REQUIRED) -->
    <div id="elfinder"></div>

    <script type="text/javascript">
        $(document).ready(function() {
            $(\'#elfinder\').elfinder({
                url : \'/filemanager/connector\',  // connector URL (REQUIRED)
                lang: \'en\',                      // language (OPTIONAL)
                width: \'100%\',                   // manager width
                height: 600,                      // manager height
                resizable: true                   // enable resizing
            });
        });
    </script>

</body>
</html>';
    }
}

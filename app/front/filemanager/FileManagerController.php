<?php namespace app\front\filemanager;

use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use app\system\AppSession;
use app\system\BaseController;

class FileManagerController extends BaseController
{
    public static function getName(): string {
        return 'filemanager';
    }

    public function index(Request $request): Response {
        // Return the official elFinder interface instead of custom template
        return new Response(
            $this->renderFileManagerInterface()
        );
    }

    protected function getControllerMapping(): array {
        return [
            '/filemanager' => [$this, 'index'],
            '/filemanager/connector' => [$this, 'connector'],
        ];
    }

    public function connector(Request $request): Response {
        // Handle elFinder connector requests
        error_reporting(0); // Disable error reporting for production

        // Load elFinder autoload (required for proper functioning)
        require_once __DIR__ . '/../../vendor/composer/studio-42/elfinder/php/autoload.php';

        // Enable FTP connector netmount
        \elFinder::$netDrivers['ftp'] = 'FTP';

        // Access control function
        $accessControl = function($attr, $path, $data, $volume, $isDir, $relpath) {
            $basename = basename($path);
            return $basename[0] === '.'                  // if file/folder begins with '.' (dot)
                     && strlen($relpath) !== 1           // but without volume root
                ? !($attr == 'read' || $attr == 'write') // set read+write to false, other (locked+hidden) set to true
                :  null;                                 // else elFinder decide it itself
        };

        // Create files directory if it doesn't exist
        $filesPath = __DIR__ . '/../../../files';
        $trashPath = $filesPath . '/.trash';
        $tmbPath = $filesPath . '/.tmb';

        if (!is_dir($filesPath)) {
            mkdir($filesPath, 0755, true);
        }
        if (!is_dir($trashPath)) {
            mkdir($trashPath, 0755, true);
        }
        if (!is_dir($tmbPath)) {
            mkdir($tmbPath, 0755, true);
        }

        // elFinder configuration options
        $opts = array(
            'roots' => array(
                // Main files volume
                array(
                    'driver'        => 'LocalFileSystem',
                    'path'          => $filesPath,
                    'URL'           => '/files/',
                    'trashHash'     => 't1_Lw',
                    'winHashFix'    => DIRECTORY_SEPARATOR !== '/',
                    'uploadDeny'    => array('all'),
                    'uploadAllow'   => array(
                        'image/x-ms-bmp', 'image/gif', 'image/jpeg', 'image/png', 'image/x-icon',
                        'text/plain', 'text/html', 'text/css', 'text/javascript',
                        'application/pdf', 'application/zip',
                        'video/mp4', 'video/avi', 'video/quicktime',
                        'audio/mp3', 'audio/wav'
                    ),
                    'uploadOrder'   => array('deny', 'allow'),
                    'accessControl' => $accessControl
                ),
                // Trash volume
                array(
                    'id'            => '1',
                    'driver'        => 'Trash',
                    'path'          => $trashPath,
                    'tmbURL'        => '/files/.trash/.tmb/',
                    'winHashFix'    => DIRECTORY_SEPARATOR !== '/',
                    'uploadDeny'    => array('all'),
                    'uploadAllow'   => array(
                        'image/x-ms-bmp', 'image/gif', 'image/jpeg', 'image/png', 'image/x-icon',
                        'text/plain', 'text/html', 'text/css', 'text/javascript',
                        'application/pdf', 'application/zip',
                        'video/mp4', 'video/avi', 'video/quicktime',
                        'audio/mp3', 'audio/wav'
                    ),
                    'uploadOrder'   => array('deny', 'allow'),
                    'accessControl' => $accessControl,
                ),
            )
        );

        // Run elFinder
        $connector = new \elFinderConnector(new \elFinder($opts));

        // Capture output
        ob_start();
        $connector->run();
        $output = ob_get_clean();

        return new Response($output, 200, [
            'Content-Type' => 'application/json'
        ]);
    }

    private function renderFileManagerInterface(): string {
        $elfinderPath = '/app/vendor/composer/studio-42/elfinder';

        return '<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=2">
    <title>File Manager - VisualBook</title>

    <!-- jQuery (REQUIRED) -->
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
    <script src="https://ajax.googleapis.com/ajax/libs/jqueryui/1.13.2/jquery-ui.min.js"></script>

    <!-- elFinder CSS (REQUIRED) -->
    <link rel="stylesheet" type="text/css" href="' . $elfinderPath . '/css/elfinder.min.css">
    <link rel="stylesheet" type="text/css" href="' . $elfinderPath . '/css/theme.css">

    <!-- elFinder JS (REQUIRED) -->
    <script src="' . $elfinderPath . '/js/elfinder.min.js"></script>

    <!-- elFinder translation (OPTIONAL) -->
    <script src="' . $elfinderPath . '/js/i18n/elfinder.en.js"></script>

    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background-color: #f5f5f5;
        }
        .header {
            background: white;
            padding: 20px;
            margin-bottom: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            margin: 0 0 10px 0;
            font-size: 24px;
        }
        .subtitle {
            color: #666;
            font-size: 14px;
            margin: 0;
        }
        #elfinder {
            border: 1px solid #ddd;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .navigation {
            margin-top: 20px;
            text-align: center;
        }
        .nav-link {
            display: inline-block;
            padding: 10px 20px;
            background: #007cba;
            color: white;
            text-decoration: none;
            border-radius: 4px;
            margin: 0 5px;
        }
        .nav-link:hover {
            background: #005a87;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>📁 File Manager</h1>
        <p class="subtitle">Manage your files with the official elFinder interface</p>
    </div>

    <!-- Element where elFinder will be created (REQUIRED) -->
    <div id="elfinder"></div>

    <div class="navigation">
        <a href="/admin" class="nav-link">← Back to Admin Panel</a>
        <a href="/upload" class="nav-link">📤 Upload Page</a>
    </div>

    <script type="text/javascript">
        $(document).ready(function() {
            $(\'#elfinder\').elfinder({
                url : \'/filemanager/connector\',  // connector URL (REQUIRED)
                lang: \'en\',                      // language (OPTIONAL)
                width: \'100%\',                   // manager width
                height: 600,                      // manager height
                resizable: true,                  // enable resizing
                rememberLastDir: true,            // remember last opened directory
                useBrowserHistory: true,          // use browser back/forward buttons
                contextmenu : {
                    // navbarfolder menu
                    navbar : [\'open\', \'copy\', \'cut\', \'paste\', \'duplicate\', \'|\', \'rm\', \'empty\', \'|\', \'info\'],
                    // current directory menu
                    cwd    : [\'reload\', \'back\', \'|\', \'upload\', \'mkdir\', \'mkfile\', \'paste\', \'|\', \'info\'],
                    // current directory file menu
                    files  : [\'getfile\', \'|\', \'open\', \'quicklook\', \'|\', \'download\', \'|\', \'copy\', \'cut\', \'paste\', \'duplicate\', \'|\', \'rm\', \'|\', \'edit\', \'rename\', \'resize\', \'|\', \'archive\', \'extract\', \'|\', \'info\']
                },
                ui: [\'toolbar\', \'tree\', \'path\', \'stat\'],
                uiOptions : {
                    toolbar : [
                        [\'back\', \'forward\'],
                        [\'reload\'],
                        [\'home\', \'up\'],
                        [\'mkdir\', \'mkfile\', \'upload\'],
                        [\'open\', \'download\', \'getfile\'],
                        [\'info\'],
                        [\'quicklook\'],
                        [\'copy\', \'cut\', \'paste\'],
                        [\'rm\'],
                        [\'duplicate\', \'rename\', \'edit\', \'resize\'],
                        [\'extract\', \'archive\'],
                        [\'search\'],
                        [\'view\'],
                        [\'help\']
                    ]
                }
            });
        });
    </script>

</body>
</html>';
    }
}
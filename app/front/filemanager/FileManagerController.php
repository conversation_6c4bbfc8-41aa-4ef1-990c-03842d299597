<?php namespace app\front\filemanager;

use app\system\BaseController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Route;
use Symfony\Component\Routing\RouteCollection;

class FileManagerController extends BaseController {

    protected function getControllerMapping(): array {
        return [
            '/filemanager' => [$this, 'index'],
            '/filemanager/connector' => [$this, 'connector'],
        ];
    }

    public function index(Request $request): Response {
        try {
            // Render the file manager interface
            $html = $this->renderFileManagerInterface();
            return new Response($html);
        } catch (\Exception $e) {
            return new Response('Error: ' . $e->getMessage() . '<br>File: ' . $e->getFile() . '<br>Line: ' . $e->getLine(), 500);
        }
    }

    public function connector(Request $request): Response {
        // Handle elFinder connector requests
        error_reporting(0); // Set E_ALL for debugging if needed

        // elFinder classes are already loaded via the main autoloader

        // Enable FTP connector netmount
        \elFinder::$netDrivers['ftp'] = 'FTP';

        // Access control function
        $accessControl = function($attr, $path, $data, $volume, $isDir, $relpath) {
            $basename = basename($path);
            return $basename[0] === '.'                  // if file/folder begins with '.' (dot)
                     && strlen($relpath) !== 1           // but without volume root
                ? !($attr == 'read' || $attr == 'write') // set read+write to false, other (locked+hidden) set to true
                :  null;                                 // else elFinder decide it itself
        };

        // Configuration options
        $opts = array(
            // 'debug' => true,
            'roots' => array(
                // Items volume
                array(
                    'driver'        => 'LocalFileSystem',           // driver for accessing file system (REQUIRED)
                    'path'          => realpath(__DIR__ . '/../../../files/'), // path to files (REQUIRED)
                    'URL'           => '/files/',                   // URL to files (REQUIRED)
                    'trashHash'     => 't1_Lw',                     // elFinder's hash of trash folder
                    'winHashFix'    => DIRECTORY_SEPARATOR !== '/', // to make hash same to Linux one on windows too
                    'uploadDeny'    => array('all'),                // All Mimetypes not allowed to upload
                    'uploadAllow'   => array(
                        'image/x-ms-bmp', 'image/gif', 'image/jpeg', 'image/png', 'image/x-icon', 
                        'text/plain', 'text/html', 'text/css', 'text/javascript', 'application/javascript',
                        'application/pdf', 'application/zip', 'application/x-zip-compressed',
                        'video/mp4', 'video/avi', 'video/quicktime', 'audio/mpeg', 'audio/wav'
                    ), // Extended allowed file types
                    'uploadOrder'   => array('deny', 'allow'),      // allowed Mimetype processing order
                    'accessControl' => $accessControl               // disable and hide dot starting files (OPTIONAL)
                ),
                // Trash volume
                array(
                    'id'            => '1',
                    'driver'        => 'Trash',
                    'path'          => realpath(__DIR__ . '/../../../files/.trash/'),
                    'tmbURL'        => '/files/.trash/.tmb/',
                    'winHashFix'    => DIRECTORY_SEPARATOR !== '/', // to make hash same to Linux one on windows too
                    'uploadDeny'    => array('all'),                // Recommend the same settings as the original volume that uses the trash
                    'uploadAllow'   => array(
                        'image/x-ms-bmp', 'image/gif', 'image/jpeg', 'image/png', 'image/x-icon', 
                        'text/plain', 'text/html', 'text/css', 'text/javascript', 'application/javascript',
                        'application/pdf', 'application/zip', 'application/x-zip-compressed',
                        'video/mp4', 'video/avi', 'video/quicktime', 'audio/mpeg', 'audio/wav'
                    ), // Same as above
                    'uploadOrder'   => array('deny', 'allow'),      // Same as above
                    'accessControl' => $accessControl,              // Same as above
                ),
            )
        );

        // Run elFinder
        $connector = new \elFinderConnector(new \elFinder($opts));
        
        // Capture output
        ob_start();
        $connector->run();
        $output = ob_get_clean();
        
        return new Response($output, 200, [
            'Content-Type' => 'application/json'
        ]);
    }

    private function renderFileManagerInterface(): string {
        $elfinderPath = '/app/vendor/composer/studio-42/elfinder';

        return '<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>File Manager - VisualBook</title>
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=2" />

    <!-- jQuery (REQUIRED) -->
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>

    <!-- elFinder CSS (REQUIRED) -->
    <link rel="stylesheet" type="text/css" href="' . $elfinderPath . '/css/elfinder.min.css">
    <link rel="stylesheet" type="text/css" href="' . $elfinderPath . '/css/theme.css">

    <!-- elFinder JS (REQUIRED) -->
    <script src="' . $elfinderPath . '/js/elfinder.min.js"></script>

    <!-- elFinder translation (OPTIONAL) -->
    <script src="' . $elfinderPath . '/js/i18n/elfinder.en.js"></script>

    <style>
        body { margin: 0; padding: 20px; font-family: Arial, sans-serif; }
        h1 { color: #333; margin-bottom: 20px; }
        #elfinder { border: 1px solid #ccc; }
    </style>
</head>
<body>
    <h1>File Manager</h1>

    <!-- Element where elFinder will be created (REQUIRED) -->
    <div id="elfinder"></div>

    <script type="text/javascript">
        $(document).ready(function() {
            $(\'#elfinder\').elfinder({
                url : \'/filemanager/connector\',  // connector URL (REQUIRED)
                lang: \'en\',                      // language (OPTIONAL)
                width: \'100%\',                   // manager width
                height: 600,                      // manager height
                resizable: true                   // enable resizing
            });
        });
    </script>

</body>
</html>';
    }
}

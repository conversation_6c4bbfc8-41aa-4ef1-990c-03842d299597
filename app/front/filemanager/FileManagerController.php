<?php namespace app\front\filemanager;

use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use app\system\AppSession;
use app\system\BaseController;

class FileManagerController extends BaseController
{
    public static function getName(): string {
        return 'filemanager';
    }

    public function index(Request $request): Response {
        if ($request->getMethod() === 'POST') {
            $page = new FileManagerPage();
            $page->checkPost($request->request->all());
        }

        return new Response(
            FileManagerPage::renderString()
        );
    }

    protected function getControllerMapping(): array {
        return [
            '/filemanager' => [$this, 'index'],
            '/filemanager/connector' => [$this, 'connector'],
        ];
    }

    public function connector(Request $request): Response {
        try {
            // Handle elFinder connector requests
            error_reporting(0); // Disable error reporting for production

            // Set time limit to prevent 503 errors
            set_time_limit(30);

            // Enable FTP connector netmount
            \elFinder::$netDrivers['ftp'] = 'FTP';

        // Access control function
        $accessControl = function($attr, $path, $data, $volume, $isDir, $relpath) {
            $basename = basename($path);
            return $basename[0] === '.'                  // if file/folder begins with '.' (dot)
                     && strlen($relpath) !== 1           // but without volume root
                ? !($attr == 'read' || $attr == 'write') // set read+write to false, other (locked+hidden) set to true
                :  null;                                 // else elFinder decide it itself
        };

        // Sync database files to local directory for elFinder (efficient approach)
        $this->syncDatabaseFilesToLocal();

        // Configuration options
        $opts = array(
            'debug' => false,
            'bind' => array(
                'rm' => array($this, 'onFileDelete'),
                'rename' => array($this, 'onFileRename'),
            ),
            'roots' => array(
                // Items volume
                array(
                    'driver'        => 'LocalFileSystem',
                    'path'          => realpath(__DIR__ . '/../../../files/'),
                    'URL'           => '/files/',
                    'trashHash'     => 't1_Lw',
                    'winHashFix'    => DIRECTORY_SEPARATOR !== '/',
                    'uploadDeny'    => array('all'),
                    'uploadAllow'   => array(
                        'image/x-ms-bmp', 'image/gif', 'image/jpeg', 'image/png', 'image/x-icon',
                        'text/plain', 'application/pdf', 'application/zip'
                    ),
                    'uploadOrder'   => array('deny', 'allow'),
                    'accessControl' => $accessControl
                ),
                // Trash volume
                array(
                    'id'            => '1',
                    'driver'        => 'Trash',
                    'path'          => realpath(__DIR__ . '/../../../files/.trash/'),
                    'tmbURL'        => '/files/.trash/.tmb/',
                    'winHashFix'    => DIRECTORY_SEPARATOR !== '/',
                    'uploadDeny'    => array('all'),
                    'uploadAllow'   => array(
                        'image/x-ms-bmp', 'image/gif', 'image/jpeg', 'image/png', 'image/x-icon',
                        'text/plain', 'application/pdf', 'application/zip'
                    ),
                    'uploadOrder'   => array('deny', 'allow'),
                    'accessControl' => $accessControl,
                ),
            )
        );

        // Run elFinder
        $connector = new \elFinderConnector(new \elFinder($opts));

        // Capture output
        ob_start();
        $connector->run();
        $output = ob_get_clean();

        return new Response($output, 200, [
            'Content-Type' => 'application/json'
        ]);

        } catch (\Exception $e) {
            // Return error response to prevent 503 errors
            return new Response('{"error":["Server error: ' . addslashes($e->getMessage()) . '"]}', 200, [
                'Content-Type' => 'application/json'
            ]);
        }
    }

    private function syncDatabaseFilesToLocal() {
        $filesDir = realpath(__DIR__ . '/../../../files/');
        if (!$filesDir) {
            mkdir(__DIR__ . '/../../../files/', 0755, true);
            $filesDir = realpath(__DIR__ . '/../../../files/');
        }

        // Get files from database
        $files = \dibi::query("SELECT * FROM files ORDER BY upload_date DESC")->fetchAll();

        // Create a cache file to track sync status
        $cacheFile = $filesDir . '/.sync_cache';
        $lastSync = file_exists($cacheFile) ? filemtime($cacheFile) : 0;
        $currentTime = time();

        // Only sync if it's been more than 10 minutes since last sync (performance optimization)
        // Or if this is the first time (no cache file exists)
        if ($currentTime - $lastSync < 600 && $lastSync > 0) {
            return;
        }

        // Clear old placeholder files first
        $existingFiles = glob($filesDir . '/*');
        foreach ($existingFiles as $file) {
            if (is_file($file) && basename($file) !== '.sync_cache' && !str_starts_with(basename($file), '.')) {
                unlink($file);
            }
        }

        // Create lightweight placeholder files for each database file
        foreach ($files as $file) {
            $localFile = $filesDir . '/' . $file->filename;

            // Create a small placeholder file with proper extension
            $extension = strtolower(pathinfo($file->filename, PATHINFO_EXTENSION));

            if (in_array($extension, ['jpg', 'jpeg', 'png', 'gif', 'bmp'])) {
                // For images, create a small placeholder image
                $this->createImagePlaceholder($localFile, $extension, $file->filename);
            } else {
                // For other files, create a text placeholder
                $content = "Remote File: {$file->filename}\n";
                $content .= "Size: {$file->filesize} bytes\n";
                $content .= "Type: {$file->filetype}\n";
                $content .= "Remote URL: {$file->url}\n";
                $content .= "Upload Date: {$file->upload_date}\n";
                file_put_contents($localFile, $content);
            }

            // Set file modification time to match upload date
            if ($file->upload_date) {
                touch($localFile, strtotime($file->upload_date));
            }
        }

        // Update cache file
        touch($cacheFile);
    }

    private function createImagePlaceholder($localFile, $extension, $filename) {
        // Create a small placeholder image
        $width = 200;
        $height = 150;
        $image = imagecreate($width, $height);

        // Colors
        $bg = imagecolorallocate($image, 240, 240, 240);
        $border = imagecolorallocate($image, 200, 200, 200);
        $text = imagecolorallocate($image, 100, 100, 100);

        // Draw border
        imagerectangle($image, 0, 0, $width-1, $height-1, $border);

        // Add text
        $shortName = strlen($filename) > 20 ? substr($filename, 0, 17) . '...' : $filename;
        imagestring($image, 3, 10, 60, 'Remote Image:', $text);
        imagestring($image, 2, 10, 80, $shortName, $text);
        imagestring($image, 1, 10, 100, 'Click to view online', $text);

        // Save image
        switch ($extension) {
            case 'png':
                imagepng($image, $localFile);
                break;
            case 'gif':
                imagegif($image, $localFile);
                break;
            default:
                imagejpeg($image, $localFile, 80);
        }

        imagedestroy($image);
    }

    public function onFileDelete($cmd, &$result, $args, $elfinder, $volume) {
        // When a file is deleted in elFinder, also delete it from the database
        if (isset($args['targets'])) {
            foreach ($args['targets'] as $target) {
                $file = $volume->file($target);
                if ($file && isset($file['name'])) {
                    // Delete from database
                    \dibi::delete('files')->where('filename = %s', $file['name'])->execute();
                }
            }
        }
    }

    public function onFileRename($cmd, &$result, $args, $elfinder, $volume) {
        // When a file is renamed in elFinder, update it in the database
        if (isset($args['target']) && isset($args['name'])) {
            $file = $volume->file($args['target']);
            if ($file && isset($file['name'])) {
                $oldName = $file['name'];
                $newName = $args['name'];

                // Update database
                \dibi::update('files', ['filename' => $newName])
                    ->where('filename = %s', $oldName)
                    ->execute();
            }
        }
    }

    private function renderFileManagerInterface(): string {
        $elfinderPath = '/app/vendor/composer/studio-42/elfinder';

        return '<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>File Manager - VisualBook</title>
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=2" />

    <!-- jQuery (REQUIRED) -->
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>

    <!-- elFinder CSS (REQUIRED) -->
    <link rel="stylesheet" type="text/css" href="' . $elfinderPath . '/css/elfinder.min.css">
    <link rel="stylesheet" type="text/css" href="' . $elfinderPath . '/css/theme.css">

    <!-- elFinder JS (REQUIRED) -->
    <script src="' . $elfinderPath . '/js/elfinder.min.js"></script>

    <!-- elFinder translation (OPTIONAL) -->
    <script src="' . $elfinderPath . '/js/i18n/elfinder.en.js"></script>

    <style>
        body { margin: 0; padding: 20px; font-family: Arial, sans-serif; }
        h1 { color: #333; margin-bottom: 20px; }
        #elfinder { border: 1px solid #ccc; }
    </style>
</head>
<body>
    <h1>File Manager</h1>

    <!-- Element where elFinder will be created (REQUIRED) -->
    <div id="elfinder"></div>

    <script type="text/javascript">
        $(document).ready(function() {
            $(\'#elfinder\').elfinder({
                url : \'/filemanager/connector\',  // connector URL (REQUIRED)
                lang: \'en\',                      // language (OPTIONAL)
                width: \'100%\',                   // manager width
                height: 600,                      // manager height
                resizable: true                   // enable resizing
            });
        });
    </script>

</body>
</html>';
    }
}

<div class="viewport">
    <div class="page-title">
        <h1 class="font-dm">File Manager</h1>
        <p class="text-gray-600">Manage your files and folders</p>
    </div>

    <!-- elFinder File Manager Interface -->
    <div class="elfinder-container mb-8">
        <div id="elfinder" style="height: 600px; border: 1px solid #ccc; border-radius: 5px;"></div>
    </div>

    <!-- File Statistics -->
    <div class="bg-blue-50 p-4 rounded-lg mb-6">
        <h3 class="text-lg font-semibold mb-2">📊 File Statistics</h3>
        <p>Total files: <strong>{$filesCount}</strong></p>
        <p>Storage location: <code>/files/</code></p>
    </div>

    <!-- Quick Actions -->
    <div class="bg-gray-50 p-4 rounded-lg mb-6">
        <h3 class="text-lg font-semibold mb-3">⚡ Quick Actions</h3>
        <div class="flex gap-4">
            <button onclick="openUploadDialog()" class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded">
                📤 Upload Files
            </button>
            <button onclick="createNewFolder()" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded">
                📁 New Folder
            </button>
            <button onclick="refreshFileManager()" class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded">
                🔄 Refresh
            </button>
        </div>
    </div>

    <!-- File List (Fallback/Additional Info) -->
    {if $files}
    <div class="bg-white rounded-lg shadow overflow-hidden">
        <div class="px-4 py-3 bg-gray-50 border-b">
            <h3 class="text-lg font-semibold">📋 File List</h3>
        </div>
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Size</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Modified</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    {foreach $files as $file}
                    <tr class="hover:bg-gray-50">
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="flex items-center">
                                <span class="text-sm font-medium text-gray-900">{$file['name']|escape}</span>
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {if $file['size'] < 1024}
                                {$file['size']} B
                            {elseif $file['size'] < 1048576}
                                {($file['size'] / 1024)|round:1} KB
                            {else}
                                {($file['size'] / 1048576)|round:1} MB
                            {/if}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {$file['type']|upper}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {$file['modified']|date:'Y-m-d H:i'}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            <a href="{$file['url']}" target="_blank" class="text-blue-600 hover:text-blue-900 mr-3">View</a>
                            <a href="{$file['url']}" download class="text-green-600 hover:text-green-900">Download</a>
                        </td>
                    </tr>
                    {/foreach}
                </tbody>
            </table>
        </div>
    </div>
    {else}
    <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
        <p class="text-yellow-800">No files found. Use the file manager above to upload files.</p>
    </div>
    {/if}

    <!-- Navigation -->
    <div class="mt-8 flex gap-4">
        <a href="/admin" class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded">
            ← Back to Admin Panel
        </a>
        <a href="/upload" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded">
            📤 Upload Page
        </a>
    </div>
</div>

<!-- Include elFinder CSS and JS -->
<link rel="stylesheet" type="text/css" href="/app/vendor/composer/studio-42/elfinder/css/elfinder.min.css">
<link rel="stylesheet" type="text/css" href="/app/vendor/composer/studio-42/elfinder/css/theme.css">

<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
<script src="/app/vendor/composer/studio-42/elfinder/js/elfinder.min.js"></script>
<script src="/app/vendor/composer/studio-42/elfinder/js/i18n/elfinder.en.js"></script>

<script>
$(document).ready(function() {
    // Initialize elFinder
    $('#elfinder').elfinder({
        url: '/filemanager/connector',
        lang: 'en',
        width: '100%',
        height: 600,
        resizable: true,
        handlers: {
            upload: function(event, elfinder) {
                // Refresh page after upload
                setTimeout(function() {
                    location.reload();
                }, 1000);
            }
        }
    });
});

// Quick action functions
function openUploadDialog() {
    // Trigger elFinder upload
    $('#elfinder').elfinder('exec', 'upload');
}

function createNewFolder() {
    // Trigger elFinder new folder creation
    $('#elfinder').elfinder('exec', 'mkdir');
}

function refreshFileManager() {
    // Refresh elFinder
    $('#elfinder').elfinder('reload');
    // Also refresh the page to update file list
    setTimeout(function() {
        location.reload();
    }, 500);
}
</script>

<?php namespace app\system;

/** Created by <PERSON><PERSON><PERSON><PERSON><PERSON>. Date: 15.05.2025 */
class System
{
    /**
     * Returns the project's root directory
     * This function is used to get the absolute path to the project root,
     * which is useful for setting temp directories and other path-dependent configurations
     */
    public static function getProjectRootDirectory(): string
    {
        static $rootDir;
        return $rootDir ??= realpath(__DIR__ . '/../..');
    }
}

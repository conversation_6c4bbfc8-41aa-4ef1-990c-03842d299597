<?php namespace app\system;

/** Created by <PERSON><PERSON><PERSON><PERSON><PERSON>. Date: 04.08.2021 */
class UrlCache
{

   public const STYLE_DIR = 'css/';
   public const JS_DIR = 'js/';

   public static function appendIndex(string $url) :string {
      $filePath = System::getProjectRootDirectory() . '/public/' . $url;
      if(file_exists($filePath))
         $url .= '?'. filemtime($filePath);

      return $url;
   }

   public static function appendIndexCss(string $file, array $args = []) :string {
      return '<link rel="stylesheet" type="text/css" href="/' . self::appendIndex(self::STYLE_DIR . $file) . '">';
   }

   public static function appendIndexJs(string $file, array $args = []) :string {
      return '<script src="/' . self::appendIndex(self::JS_DIR . $file) . '"></script>';
   }
}
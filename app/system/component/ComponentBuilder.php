<?php namespace app\system\component;

use Exception;
use ReflectionClass;
use app\system\System;

/** Created by <PERSON><PERSON><PERSON><PERSON><PERSON>. Date: 14.11.2021 */
class ComponentBuilder
{

   public static function get(string $componentName, bool $cache = true) {
      if(class_exists($componentName)){
         $class = new ReflectionClass($componentName);

         $componentName = str_replace('\\', '/', $componentName);
         $path = substr($componentName, 0, $lastPos = strrpos($componentName, '/'));
         $name = substr($componentName, $lastPos);

         $fullPath = System::getProjectRootDirectory() . '/' . $path;
         if(!is_dir($fullPath))
            throw new Exception('Komponenta (' . $name . ') nebyla nalezena: ' . $fullPath);

         if(!$cache || !isset(self::$components[$name]))
            self::$components[$name] = self::create($class);
         return self::$components[$name];
      } else
         return null;
   }

   protected static function create(ReflectionClass $class) :Component {
      /** @var Component $component */
      self::$components[$class->getShortName()] = $component = $class->newInstance();
      $component->setPath(dirname(str_replace('\\', '/', $class->getFileName())) . '/');
      return $component;
   }

   protected static array $components;
}
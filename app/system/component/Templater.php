<?php namespace app\system\component;

use app\system\component\extensions\macros\file\FileExtension;
use app\system\Files;
use Exception;
use Latte\Engine;
use Latte\Runtime\Html;

/** Created by <PERSON><PERSON><PERSON><PERSON><PERSON>. Date: 14.11.2021 */
class Templater
{

   public function __construct() {
      if(!isset($this->engine)){
         $this->engine = new Engine();
         $this->engine->addFilter('json_encode', fn($val) => json_encode($val));
         $this->setTempDirectory();
         $this->prepareCustomExtensions();
      }
   }

   public function __toString() {
      try{
         return $this->render();
      }catch(Exception $e){
         return $e->getMessage();
      }
   }

   public function addData(array $add) :Templater {
      $this->data = array_merge($this->data, $add);
      return $this;
   }

   private static array $templaterCache;

   public static function prepare(string $filePath, array $data = array(), bool $cacheEnable = true) :self {
      $cached = &self::$templaterCache[crc32($filePath)];

      if(!$cacheEnable || !isset($cached)){
         $cached = new self();
         $cached->setTemplateFile($filePath)
            ->setData($data);
      }

      return $cached;
   }

   protected function setTemplateFile(string $filePath) :Templater {
      if (!file_exists($filePath)) {
   throw new Exception('Latte file not found: ' . $filePath);
}

      $this->file = $filePath;
      return $this;
   }

   protected function setData(array $prepareData) :Templater {
      $this->data = $prepareData;
      return $this;
   }

   private function setTempDirectory() :void {
      Files::checkDir($dir = Files::getLatteTempDir());
      $this->engine->setTempDirectory($dir);
   }

   public function render(bool $echo = false) :string {
    return $echo
       ? $this->echoTemplate()
       : $this->returnString();
   }

   public function renderHtml() :Html {
      return new Html($this->render());
   }

   private function echoTemplate() :string {
      if(preg_match('~\.html$~', $this->file)){
         echo file_get_contents($this->file);
         return '';
      }

      if(!is_file($this->file))
         throw new Exception($this->file . ': není soubor');

      $this->engine->render($this->file, $this->data);
      return '';
   }

   private function returnString() :string {
      ob_start();
      $this->echoTemplate();
      return ob_get_clean();
   }

   protected function prepareCustomExtensions() :void {
      $this->prepareMacros();
   }

   private function prepareMacros() :void {
      $this->engine->addExtension(new FileExtension());
   }

   private Engine $engine;
   protected string $file;
   protected array $data = array();
}